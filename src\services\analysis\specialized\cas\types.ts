/**
 * CAS (Create-A-Sim) Analysis Types and Enums
 * 
 * This file contains all type definitions, interfaces, and enums
 * related to CAS Part analysis and categorization.
 * 
 * Extracted from CASPartAnalyzer.ts as part of Phase 2 refactoring
 * to follow the Single Responsibility Principle.
 */

/**
 * Detailed CAS Part information extracted from actual resource data
 * ENHANCED: Added Reddit-requested features for conflict detection, mesh relationships, and visual management
 */
export interface CASPartInfo {
    category: CASCategory;
    subcategory: string;
    ageGroups: AgeGroup[];
    genders: Gender[];
    clothingCategories: ClothingCategory[];
    bodyLocation: BodyLocation[];
    isAccessory: boolean;
    isHair: boolean;
    isMakeup: boolean;
    isClothing: boolean;
    tags: string[];
    description: string;

    // ENHANCEMENT: Conflict Detection & Validation
    conflicts: CASConflictInfo;
    validation: CASValidationInfo;

    // ENHANCEMENT: Mesh-Recolor Relationships
    meshRelationships: MeshRecolorRelationship;

    // ENHANCEMENT: Visual Management
    visual: CASVisualInfo;

    // ENHANCEMENT: Dependencies
    dependencies: CASDependencyInfo;
}

/**
 * CAS main categories
 */
export enum CASCategory {
    HAIR = 'hair',
    CLOTHING = 'clothing', 
    MAKEUP = 'makeup',
    ACCESSORIES = 'accessories',
    BODY_HAIR = 'body_hair',
    SKIN_DETAILS = 'skin_details',
    UNKNOWN = 'unknown'
}

/**
 * Age groups for CAS content
 */
export enum AgeGroup {
    INFANT = 'infant',
    TODDLER = 'toddler', 
    CHILD = 'child',
    TEEN = 'teen',
    YOUNG_ADULT = 'young_adult',
    ADULT = 'adult',
    ELDER = 'elder'
}

/**
 * Gender categories
 */
export enum Gender {
    MALE = 'male',
    FEMALE = 'female',
    UNISEX = 'unisex'
}

/**
 * Clothing categories for outfits
 */
export enum ClothingCategory {
    EVERYDAY = 'everyday',
    FORMAL = 'formal',
    ATHLETIC = 'athletic',
    SLEEP = 'sleep',
    PARTY = 'party',
    SWIMWEAR = 'swimwear',
    HOT_WEATHER = 'hot_weather',
    COLD_WEATHER = 'cold_weather',
    BATHING = 'bathing'
}

/**
 * Body locations for clothing and accessories
 */
export enum BodyLocation {
    HEAD = 'head',
    HAIR = 'hair',
    FACE = 'face',
    UPPER_BODY = 'upper_body',
    LOWER_BODY = 'lower_body',
    FULL_BODY = 'full_body',
    FEET = 'feet',
    HANDS = 'hands',
    NECK = 'neck',
    EARS = 'ears',
    WRISTS = 'wrists',
    RINGS = 'rings'
}

/**
 * CAS Part summary information for categorization
 */
export interface CASPartSummary {
    primaryCategory: string;
    subcategory: string;
    description: string;
}

/**
 * Display names mapping for CAS categories
 */
export const CAS_CATEGORY_DISPLAY_NAMES: Record<CASCategory, string> = {
    [CASCategory.HAIR]: 'Hair',
    [CASCategory.CLOTHING]: 'Clothing',
    [CASCategory.MAKEUP]: 'Makeup',
    [CASCategory.ACCESSORIES]: 'Accessories',
    [CASCategory.BODY_HAIR]: 'Body Hair',
    [CASCategory.SKIN_DETAILS]: 'Skin Details',
    [CASCategory.UNKNOWN]: 'Unknown CAS'
};

/**
 * Age group bit flags for parsing AgeGender values
 */
export const AGE_GROUP_FLAGS = {
    INFANT: 0x0001,
    TODDLER: 0x0002,
    CHILD: 0x0004,
    TEEN: 0x0008,
    YOUNG_ADULT: 0x0010,
    ADULT: 0x0020,
    ELDER: 0x0040
} as const;

/**
 * Gender bit flags for parsing AgeGender values
 */
export const GENDER_FLAGS = {
    MALE: 0x1000,
    FEMALE: 0x2000
} as const;

/**
 * CAS Part type constants for mapping part types to categories
 */
export const CAS_PART_TYPES = {
    HAIR: 1,
    FACE_PAINT: 2,
    TOP: 4,
    BOTTOM: 8,
    FULL_BODY: 16,
    SHOES: 32,
    ACCESSORIES: 64,
    MAKEUP: 128
} as const;

/**
 * Default fallback CAS part info for when analysis fails
 */
export const DEFAULT_CAS_PART_INFO: CASPartInfo = {
    category: CASCategory.UNKNOWN,
    subcategory: 'unknown',
    ageGroups: [AgeGroup.TEEN, AgeGroup.YOUNG_ADULT, AgeGroup.ADULT, AgeGroup.ELDER],
    genders: [Gender.UNISEX],
    clothingCategories: [ClothingCategory.EVERYDAY],
    bodyLocation: [],
    isAccessory: false,
    isHair: false,
    isMakeup: false,
    isClothing: true,
    tags: [],
    description: 'Unknown CAS content'
};

/**
 * CAS resource type identifier
 */
export const CAS_PART_RESOURCE_TYPE = 0x034AEECB;

/**
 * ENHANCEMENT: Conflict Detection Information
 * Addresses Reddit request: "flag conflicting mods"
 */
export interface CASConflictInfo {
    hasConflicts: boolean;
    conflictType: ConflictType[];
    conflictingFiles: string[];
    conflictSeverity: ConflictSeverity;
    conflictDetails: string[];
}

export enum ConflictType {
    DUPLICATE_RESOURCE = 'duplicate_resource',
    TEXTURE_CLASH = 'texture_clash',
    MESH_CONFLICT = 'mesh_conflict',
    CATEGORY_OVERLAP = 'category_overlap',
    DEPENDENCY_CONFLICT = 'dependency_conflict'
}

export enum ConflictSeverity {
    LOW = 'low',           // Minor visual issues
    MEDIUM = 'medium',     // Noticeable problems
    HIGH = 'high',         // Broken functionality
    CRITICAL = 'critical'  // Game crashes/corruption
}

/**
 * ENHANCEMENT: Validation Information
 * Addresses Reddit request: "detect broken cc and texture clashes"
 */
export interface CASValidationInfo {
    isValid: boolean;
    isBroken: boolean;
    hasTextureClash: boolean;
    hasMissingMesh: boolean;
    validationIssues: ValidationIssue[];
    autoDeleteRecommended: boolean;
    repairSuggestions: string[];
}

export interface ValidationIssue {
    type: ValidationIssueType;
    severity: ConflictSeverity;
    description: string;
    affectedResources: string[];
    autoFixAvailable: boolean;
}

export enum ValidationIssueType {
    MISSING_TEXTURE = 'missing_texture',
    CORRUPTED_MESH = 'corrupted_mesh',
    INVALID_RESOURCE = 'invalid_resource',
    BROKEN_REFERENCE = 'broken_reference',
    TEXTURE_CLASH = 'texture_clash',
    MISSING_DEPENDENCY = 'missing_dependency'
}

/**
 * ENHANCEMENT: Mesh-Recolor Relationship Information
 * Addresses Reddit request: "identify if something is a recolor, or a mesh is missing"
 */
export interface MeshRecolorRelationship {
    isMesh: boolean;
    isRecolor: boolean;
    meshFile?: string;
    recolorFiles: string[];
    meshHash?: string;
    recolorHashes: string[];
    relationshipType: MeshRelationshipType;
    dependsOnMesh: boolean;
    requiredForRecolors: string[];
}

export enum MeshRelationshipType {
    STANDALONE = 'standalone',        // Independent item
    MESH_ONLY = 'mesh_only',         // Mesh without recolors
    RECOLOR_ONLY = 'recolor_only',   // Recolor requiring mesh
    MESH_WITH_RECOLORS = 'mesh_with_recolors', // Mesh with associated recolors
    RECOLOR_SET = 'recolor_set'      // Multiple recolors for same mesh
}

/**
 * ENHANCEMENT: Visual Management Information
 * Addresses Reddit request: "visual categories with thumbnails and sidebar navigation"
 */
export interface CASVisualInfo {
    thumbnailPath?: string;
    thumbnailGenerated: boolean;
    previewAvailable: boolean;
    visualTags: string[];
    colorSwatches: ColorSwatch[];
    displayCategory: string;
    sortOrder: number;
    customTags: string[];
}

export interface ColorSwatch {
    color: string;        // Hex color code
    name?: string;        // Color name if available
    isPrimary: boolean;   // Main color of the item
}

/**
 * ENHANCEMENT: Dependency Information
 * Addresses Reddit request: "missing needed mod (like Lot51's core mod)"
 */
export interface CASDependencyInfo {
    requiredMods: RequiredMod[];
    optionalMods: RequiredMod[];
    hasUnmetDependencies: boolean;
    dependencyWarnings: string[];
    coreModsRequired: CoreModType[];
}

export interface RequiredMod {
    modName: string;
    author?: string;
    version?: string;
    isInstalled: boolean;
    installPath?: string;
    downloadUrl?: string;
    isCore: boolean;
}

export enum CoreModType {
    LOT51_CORE = 'lot51_core',
    LUMPINOU_MOOD_PACK = 'lumpinou_mood_pack',
    BASEMENTAL_DRUGS = 'basemental_drugs',
    WICKED_WHIMS = 'wicked_whims',
    WONDERFUL_WHIMS = 'wonderful_whims',
    MCCC = 'mccc',
    UI_CHEATS = 'ui_cheats'
}
