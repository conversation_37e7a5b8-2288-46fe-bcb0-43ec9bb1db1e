/**
 * Enhanced Metadata Extraction Service
 * 
 * Main orchestrator for multi-source metadata extraction.
 * Coordinates all extractors and provides unified interface.
 */

import { Package } from '@s4tk/models';
import { URT } from '../../../constants/unifiedResourceTypes';
import { DEFAULT_METADATA_CONFIG, type MetadataExtractionConfig } from '../../../config/metadata-extraction.config';

import { FilenameMetadataExtractor } from './FilenameMetadataExtractor';
import { CustomStringTableParser } from './CustomStringTableParser';
import { TuningMetadataExtractor } from './TuningMetadataExtractor';
import { SimDataMetadataExtractor } from './SimDataMetadataExtractor';
import { ManifestDetector } from './ManifestDetector';
import { MetadataAggregator, type AggregatedMetadata, type MetadataSource } from './MetadataAggregator';

export interface EnhancedMetadataExtractionOptions {
    readonly config?: Partial<MetadataExtractionConfig>;
    readonly enabledExtractors?: {
        readonly filename?: boolean;
        readonly stringTable?: boolean;
        readonly tuning?: boolean;
        readonly simData?: boolean;
        readonly manifest?: boolean;
    };
    readonly maxProcessingTime?: number;
    readonly enableCaching?: boolean;
}

export interface ExtractionResult {
    readonly metadata: AggregatedMetadata;
    readonly extractionDetails: {
        readonly filename: { attempted: boolean; successful: boolean; error?: string };
        readonly stringTable: { attempted: boolean; successful: boolean; resourceCount: number; error?: string };
        readonly tuning: { attempted: boolean; successful: boolean; resourceCount: number; error?: string };
        readonly simData: { attempted: boolean; successful: boolean; resourceCount: number; error?: string };
        readonly manifest: { attempted: boolean; successful: boolean; manifestCount: number; error?: string };
    };
    readonly performance: {
        readonly totalTime: number;
        readonly extractorTimes: Record<string, number>;
        readonly aggregationTime: number;
    };
}

/**
 * Enhanced metadata extraction service
 */
export class EnhancedMetadataExtractionService {
    private readonly config: MetadataExtractionConfig;
    private readonly filenameExtractor: FilenameMetadataExtractor;
    private readonly stringTableParser: CustomStringTableParser;
    private readonly tuningExtractor: TuningMetadataExtractor;
    private readonly simDataExtractor: SimDataMetadataExtractor;
    private readonly manifestDetector: ManifestDetector;
    private readonly aggregator: MetadataAggregator;
    
    constructor(options: EnhancedMetadataExtractionOptions = {}) {
        this.config = {
            ...DEFAULT_METADATA_CONFIG,
            ...options.config
        };
        
        // Initialize extractors with shared config
        this.filenameExtractor = new FilenameMetadataExtractor({ config: this.config });
        this.stringTableParser = new CustomStringTableParser(this.config);
        this.tuningExtractor = new TuningMetadataExtractor(this.config);
        this.simDataExtractor = new SimDataMetadataExtractor(this.config);
        this.manifestDetector = new ManifestDetector(this.config);
        this.aggregator = new MetadataAggregator(this.config);
    }
    
    /**
     * Extracts metadata from package file
     */
    async extractMetadata(
        packageBuffer: Buffer,
        filename: string,
        options: EnhancedMetadataExtractionOptions = {}
    ): Promise<ExtractionResult> {
        const startTime = performance.now();
        const enabledExtractors = {
            filename: true,
            stringTable: true,
            tuning: true,
            simData: true,
            manifest: true,
            ...options.enabledExtractors
        };
        
        const extractionDetails: ExtractionResult['extractionDetails'] = {
            filename: { attempted: false, successful: false },
            stringTable: { attempted: false, successful: false, resourceCount: 0 },
            tuning: { attempted: false, successful: false, resourceCount: 0 },
            simData: { attempted: false, successful: false, resourceCount: 0 },
            manifest: { attempted: false, successful: false, manifestCount: 0 }
        };
        
        const extractorTimes: Record<string, number> = {};
        const sources: MetadataSource[] = [];
        
        try {
            // Parse package
            const s4tkPackage = Package.from(packageBuffer);
            
            // 1. Filename extraction
            if (enabledExtractors.filename) {
                const filenameStart = performance.now();
                extractionDetails.filename.attempted = true;
                
                try {
                    const filenameMetadata = await this.filenameExtractor.extractMetadata(filename);
                    if (filenameMetadata) {
                        sources.push(filenameMetadata);
                        extractionDetails.filename.successful = true;
                    }
                } catch (error) {
                    extractionDetails.filename.error = error.message;
                }
                
                extractorTimes.filename = performance.now() - filenameStart;
            }
            
            // 2. StringTable extraction
            if (enabledExtractors.stringTable) {
                const stblStart = performance.now();
                extractionDetails.stringTable.attempted = true;
                
                try {
                    const stblSources = await this.extractFromStringTables(s4tkPackage);
                    sources.push(...stblSources);
                    extractionDetails.stringTable.resourceCount = stblSources.length;
                    extractionDetails.stringTable.successful = stblSources.length > 0;
                } catch (error) {
                    extractionDetails.stringTable.error = error.message;
                }
                
                extractorTimes.stringTable = performance.now() - stblStart;
            }
            
            // 3. Tuning extraction
            if (enabledExtractors.tuning) {
                const tuningStart = performance.now();
                extractionDetails.tuning.attempted = true;
                
                try {
                    const tuningSources = await this.extractFromTuningResources(s4tkPackage);
                    sources.push(...tuningSources);
                    extractionDetails.tuning.resourceCount = tuningSources.length;
                    extractionDetails.tuning.successful = tuningSources.length > 0;
                } catch (error) {
                    extractionDetails.tuning.error = error.message;
                }
                
                extractorTimes.tuning = performance.now() - tuningStart;
            }
            
            // 4. SimData extraction
            if (enabledExtractors.simData) {
                const simDataStart = performance.now();
                extractionDetails.simData.attempted = true;
                
                try {
                    const simDataSources = await this.extractFromSimDataResources(s4tkPackage);
                    sources.push(...simDataSources);
                    extractionDetails.simData.resourceCount = simDataSources.length;
                    extractionDetails.simData.successful = simDataSources.length > 0;
                } catch (error) {
                    extractionDetails.simData.error = error.message;
                }
                
                extractorTimes.simData = performance.now() - simDataStart;
            }
            
            // 5. Manifest detection
            if (enabledExtractors.manifest) {
                const manifestStart = performance.now();
                extractionDetails.manifest.attempted = true;
                
                try {
                    const manifestSources = await this.extractFromManifests(s4tkPackage);
                    sources.push(...manifestSources);
                    extractionDetails.manifest.manifestCount = manifestSources.length;
                    extractionDetails.manifest.successful = manifestSources.length > 0;
                } catch (error) {
                    extractionDetails.manifest.error = error.message;
                }
                
                extractorTimes.manifest = performance.now() - manifestStart;
            }
            
            // Aggregate all sources
            const aggregationStart = performance.now();
            const metadata = await this.aggregator.aggregateMetadata(sources);
            const aggregationTime = performance.now() - aggregationStart;
            
            const totalTime = performance.now() - startTime;
            
            return {
                metadata,
                extractionDetails,
                performance: {
                    totalTime,
                    extractorTimes,
                    aggregationTime
                }
            };
            
        } catch (error) {
            console.error('[EnhancedMetadataExtractionService] Extraction failed:', error);
            
            // Return empty result with error details
            const emptyMetadata = await this.aggregator.aggregateMetadata([]);
            
            return {
                metadata: emptyMetadata,
                extractionDetails,
                performance: {
                    totalTime: performance.now() - startTime,
                    extractorTimes,
                    aggregationTime: 0
                }
            };
        }
    }
    
    /**
     * Extracts metadata from StringTable resources
     */
    private async extractFromStringTables(s4tkPackage: Package): Promise<MetadataSource[]> {
        const sources: MetadataSource[] = [];
        
        const stblResources = Array.from(s4tkPackage.entries.values())
            .filter(entry => entry.key.type === URT.StringTable);
        
        for (const entry of stblResources) {
            try {
                const buffer = Buffer.from(entry.value as any);
                const metadata = await this.stringTableParser.parseStringTable(buffer);
                if (metadata) {
                    sources.push(metadata);
                }
            } catch (error) {
                console.warn('[EnhancedMetadataExtractionService] StringTable parsing failed:', error);
            }
        }
        
        return sources;
    }
    
    /**
     * Extracts metadata from Tuning resources
     */
    private async extractFromTuningResources(s4tkPackage: Package): Promise<MetadataSource[]> {
        const sources: MetadataSource[] = [];
        
        // Look for XML tuning resources
        const tuningResources = Array.from(s4tkPackage.entries.values())
            .filter(entry => {
                // Common tuning resource types
                return entry.key.type === 0x62E94D38 || // Tuning
                       entry.key.type === 0x545AC67A || // XML
                       entry.key.type === 0x0333406C;   // SimData (XML format)
            });
        
        for (const entry of tuningResources) {
            try {
                const xmlContent = entry.value.toString();
                const metadata = await this.tuningExtractor.extractMetadata(xmlContent, entry.id?.toString());
                if (metadata) {
                    sources.push(metadata);
                }
            } catch (error) {
                console.warn('[EnhancedMetadataExtractionService] Tuning parsing failed:', error);
            }
        }
        
        return sources;
    }
    
    /**
     * Extracts metadata from SimData resources
     */
    private async extractFromSimDataResources(s4tkPackage: Package): Promise<MetadataSource[]> {
        const sources: MetadataSource[] = [];
        
        const simDataResources = Array.from(s4tkPackage.entries.values())
            .filter(entry => entry.key.type === 0x545AC67A); // SimData type
        
        for (const entry of simDataResources) {
            try {
                const buffer = Buffer.from(entry.value as any);
                const metadata = await this.simDataExtractor.extractMetadata(buffer);
                if (metadata) {
                    sources.push(metadata);
                }
            } catch (error) {
                console.warn('[EnhancedMetadataExtractionService] SimData parsing failed:', error);
            }
        }
        
        return sources;
    }
    
    /**
     * Extracts metadata from manifests
     */
    private async extractFromManifests(s4tkPackage: Package): Promise<MetadataSource[]> {
        const sources: MetadataSource[] = [];
        
        // Collect all resources that might contain manifests
        const resourceMap = new Map<string, Buffer>();
        
        for (const entry of s4tkPackage.entries.values()) {
            try {
                const buffer = Buffer.from(entry.value as any);
                resourceMap.set(entry.id?.toString() || 'unknown', buffer);
            } catch (error) {
                // Skip resources that can't be converted to buffer
            }
        }
        
        // Detect manifests
        const manifests = await this.manifestDetector.detectManifests(resourceMap);
        
        // Parse detected manifests
        for (const manifest of manifests) {
            try {
                const metadata = await this.manifestDetector.parseManifest(manifest);
                if (metadata) {
                    sources.push(metadata);
                }
            } catch (error) {
                console.warn('[EnhancedMetadataExtractionService] Manifest parsing failed:', error);
            }
        }
        
        return sources;
    }
}
